import config from './config'
import DataManager from './managers/DataManager'
import SceneManager from './managers/SceneManager'

export default class Main {
  constructor() {
    console.log('初始化游戏')
    
    // 创建画布
    this.canvas = wx.createCanvas()
    this.ctx = this.canvas.getContext('2d')


    // 加载配置
    this.config = config
    
    // 初始化数据管理器
    this.dataManager = new DataManager()
    
    // 初始化场景管理器
    this.sceneManager = new SceneManager(this)
  }
  
  start() {
    console.log('启动游戏')
    
    // 检测并处理屏幕方向
    this.handleScreenOrientation()
    
    // 监听屏幕尺寸变化
    wx.onWindowResize(this.handleScreenOrientation.bind(this))
    
    // 显示欢迎场景
    this.sceneManager.showScene('welcome')
    
    // 开始游戏循环
    this.gameLoop()
  }
  
  gameLoop() {
    // 更新场景
    this.sceneManager.update()
    
    // 渲染场景
    this.sceneManager.render()
    
    // 循环
    requestAnimationFrame(this.gameLoop.bind(this))
  }
  
  startGame() {
    console.log('开始游戏')
    
    // 重置当前关卡的游戏数据
    this.dataManager.resetGame()
    
    // 显示游戏场景
    this.sceneManager.showScene('game')
  }

  // 添加屏幕方向处理方法
  handleScreenOrientation() {
    // 获取当前屏幕信息
    const systemInfo = wx.getSystemInfoSync()
    this.width = systemInfo.windowWidth
    this.height = systemInfo.windowHeight
    
    // 更新画布尺寸
    this.canvas.width = this.width
    this.canvas.height = this.height
    
    // 判断当前是否为横屏
    this.isLandscape = this.width > this.height
    
    console.log(`屏幕方向: ${this.isLandscape ? '横屏' : '竖屏'}, 尺寸: ${this.width}x${this.height}`)
    
    // 如果不是横屏，尝试强制横屏
    if (!this.isLandscape && systemInfo.platform !== 'devtools') {
      console.log('尝试强制横屏')
      // 在某些平台上，可以尝试使用 wx.setScreenOrientation
      if (wx.setScreenOrientation) {
        wx.setScreenOrientation({
          orientation: 'landscape',
          success: () => console.log('横屏设置成功'),
          fail: (err) => console.error('横屏设置失败', err)
        })
      }
    }
    
    // 通知场景管理器屏幕方向已更改
    if (this.sceneManager && this.sceneManager.currentScene) {
      this.sceneManager.currentScene.onScreenOrientationChange(this.isLandscape)
    }
  }

  // 添加清理方法
  cleanup() {
    // 移除屏幕尺寸变化监听
    wx.offWindowResize()
  }
} 