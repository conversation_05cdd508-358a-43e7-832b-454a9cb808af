export default class DataManager {
  constructor() {
    // 游戏关卡配置 - 基于团队射击游戏
    this.levels = [
      {
        id: 1,
        name: "基础训练",
        description: "学习游戏基础知识，包括武器系统、经济机制和基本战术",
        passingScore: 3,
        timePerQuestion: 20,
        unlocked: true,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '游戏中的经济系统货币叫什么？',
            options: ['金币', '信币(Creds)', '点券', '战斗币'],
            correctIndex: 1
          },
          {
            question: '游戏中共有多少种武器类型？',
            options: ['5种', '6种', '7种', '8种'],
            correctIndex: 2
          },
          {
            question: '在爆破模式中，进攻方携带的炸弹叫什么？',
            options: ['C4炸弹', '辐能核心(Spike)', '爆破装置', '能量炸弹'],
            correctIndex: 1
          },
          {
            question: '每回合的时间限制是多少？',
            options: ['1分钟', '1分20秒', '1分40秒', '2分钟'],
            correctIndex: 2
          },
          {
            question: '要获得比赛胜利需要先取得多少回合胜利？',
            options: ['10回合', '12回合', '13回合', '15回合'],
            correctIndex: 2
          }
        ]
      },
      {
        id: 2,
        name: "特工战术",
        description: "掌握特工技能运用和团队配合策略",
        passingScore: 4,
        timePerQuestion: 25,
        unlocked: false,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '游戏中的角色被称为什么？',
            options: ['英雄', '特工(Agent)', '战士', '操作员'],
            correctIndex: 1
          },
          {
            question: '在团队中，特工的主要作用是？',
            options: ['单独作战', '分工合作帮助队伍获胜', '收集装备', '探索地图'],
            correctIndex: 1
          },
          {
            question: '游戏特别注重玩家的什么能力？',
            options: ['移动速度', '射击精准度', '反应时间', '策略思维'],
            correctIndex: 1
          },
          {
            question: '击中头部相比击中其他部位会造成什么效果？',
            options: ['相同伤害', '略高伤害', '高出许多的伤害', '必定击杀'],
            correctIndex: 2
          },
          {
            question: '在十二轮交火后会发生什么？',
            options: ['游戏结束', '进行攻守交换', '重置比分', '进入加时赛'],
            correctIndex: 1
          }
        ]
      },
      {
        id: 3,
        name: "地图精通",
        description: "熟悉游戏地图和不同模式的玩法策略",
        passingScore: 4,
        timePerQuestion: 30,
        unlocked: false,
        completed: false,
        stars: 0,
        questions: [
          {
            question: '游戏目前共有多少张地图？',
            options: ['10张', '11张', '12张', '13张'],
            correctIndex: 2
          },
          {
            question: '以下哪张地图目前在竞技模式中被暂时移除？',
            options: ['双塔迷城', '深窟幽境', '义境空岛', '极地寒港'],
            correctIndex: 1
          },
          {
            question: '竞技模式的牌位系统中，最高等级是？',
            options: ['神话', '超凡入圣', '辐能战魂', '钻石'],
            correctIndex: 2
          },
          {
            question: '超速冲点模式中，要获胜需要先取得多少分？',
            options: ['4分', '5分', '10分', '13分'],
            correctIndex: 1
          },
          {
            question: '辐能抢攻战模式的特色是？',
            options: ['所有人使用相同武器', '所有攻击方都有辐能核心', '无限金钱', '技能冷却减半'],
            correctIndex: 1
          },
          {
            question: '团队死斗模式的获胜条件是？',
            options: ['先达到50杀', '先达到100杀', '时间结束时击杀数最多', '先达到13分'],
            correctIndex: 1
          }
        ]
      }
    ];
    
    // 添加当前关卡索引
    this.currentLevelIndex = 0;
    
    // 初始化游戏状态
    this.resetGame();
    
    // 确保第一关始终解锁
    if (this.levels.length > 0) {
      this.levels[0].unlocked = true;
    }
    
    // 从本地存储加载游戏进度
    this.loadProgress();
  }
  
  // 重置当前游戏状态
  resetGame() {
    this.gameState = {
      score: 0,
      currentQuestionIndex: 0,
      selectedOption: null,
      answered: false,
      stars: 0
    };
  }
  
  // 获取当前关卡
  getCurrentLevel() {
    return this.levels[this.currentLevelIndex];
  }
  
  // 设置当前关卡
  setCurrentLevel(index) {
    if (index >= 0 && index < this.levels.length) {
      this.currentLevelIndex = index;
      this.resetGame();
      return true;
    }
    return false;
  }
  
  // 获取所有关卡
  getAllLevels() {
    return this.levels;
  }
  
  // 解锁下一关卡
  unlockNextLevel() {
    const nextLevelIndex = this.currentLevelIndex + 1;
    if (nextLevelIndex < this.levels.length) {
      // 只有当下一关未解锁时才更新状态
      if (!this.levels[nextLevelIndex].unlocked) {
        console.log(`解锁下一关卡: ${this.levels[nextLevelIndex].name}`);
        this.levels[nextLevelIndex].unlocked = true;
        this.saveProgress();
        
        // 返回新解锁的关卡信息，用于显示提示
        return {
          newlyUnlocked: true,
          level: this.levels[nextLevelIndex]
        };
      } else {
        return {
          newlyUnlocked: false
        };
      }
    }
    return {
      newlyUnlocked: false
    };
  }
  
  // 保存游戏进度到本地存储
  saveProgress() {
    try {
      const progress = {
        levels: this.levels.map(level => ({
          id: level.id,
          unlocked: level.unlocked,
          completed: level.completed,
          stars: level.stars
        }))
      };
      wx.setStorageSync('gameProgress', JSON.stringify(progress));
      console.log('游戏进度已保存');
    } catch (e) {
      console.error('保存游戏进度失败:', e);
    }
  }
  
  // 从本地存储加载游戏进度
  loadProgress() {
    try {
      const progressData = wx.getStorageSync('gameProgress');
      if (progressData) {
        const progress = JSON.parse(progressData);
        
        // 更新关卡状态
        if (progress.levels) {
          progress.levels.forEach(savedLevel => {
            const level = this.levels.find(l => l.id === savedLevel.id);
            if (level) {
              level.unlocked = savedLevel.unlocked;
              level.completed = savedLevel.completed;
              level.stars = savedLevel.stars;
            }
          });
        }
        
        console.log('游戏进度已加载');
      }
    } catch (e) {
      console.error('加载游戏进度失败:', e);
    }
  }
  
  // 更新当前关卡的星级
  updateLevelStars() {
    const level = this.getCurrentLevel();
    const totalQuestions = level.questions.length;
    const scorePercentage = (this.gameState.score / totalQuestions) * 100;
    
    let stars = 0;
    if (scorePercentage >= 90) {
      stars = 3;
    } else if (scorePercentage >= 70) {
      stars = 2;
    } else if (scorePercentage >= 50) {
      stars = 1;
    }
    
    // 只有新的星级更高时才更新
    if (stars > level.stars) {
      level.stars = stars;
      level.completed = true;
      this.saveProgress();
    }
    
    return stars;
  }
  
  // 获取当前问题
  getCurrentQuestion() {
    const level = this.getCurrentLevel();
    return level.questions[this.gameState.currentQuestionIndex];
  }
  
  // 选择选项
  selectOption(index) {
    if (this.gameState.answered) return false;
    
    this.gameState.selectedOption = index;
    this.gameState.answered = true;
    
    const currentQuestion = this.getCurrentQuestion();
    const isCorrect = index === currentQuestion.correctIndex;
    
    if (isCorrect) {
      this.gameState.score++;
    }
    
    return isCorrect;
  }
  
  // 是否有下一题
  hasNextQuestion() {
    const level = this.getCurrentLevel();
    return this.gameState.currentQuestionIndex < level.questions.length - 1;
  }
  
  // 进入下一题
  nextQuestion() {
    if (!this.hasNextQuestion()) return false;
    
    this.gameState.currentQuestionIndex++;
    this.gameState.selectedOption = null;
    this.gameState.answered = false;
    
    return true;
  }
  
  // 获取总题目数
  getTotalQuestions() {
    const level = this.getCurrentLevel();
    return level.questions.length;
  }
  
  // 判断游戏是否通关
  isGamePassed(passingScore) {
    return this.gameState.score >= passingScore;
  }
} 