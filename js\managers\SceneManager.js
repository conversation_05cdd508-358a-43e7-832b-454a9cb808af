// 场景管理器
import WelcomeScene from '../scenes/WelcomeScene'
import GameScene from '../scenes/GameScene'
import SuccessScene from '../scenes/SuccessScene'
import FailScene from '../scenes/FailScene'
import LevelSelectScene from '../scenes/LevelSelectScene'

export default class SceneManager {
  constructor(game) {
    this.game = game
    this.currentScene = null
  }
  
  showScene(sceneName, params = {}) {
    console.log(`切换场景到: ${sceneName}`, params)
    
    // 销毁当前场景
    if (this.currentScene) {
      console.log('销毁当前场景')
      this.currentScene.destroy()
      this.currentScene = null
    }
    
    // 创建新场景
    console.log(`创建新场景: ${sceneName}`)
    switch(sceneName) {
      case 'welcome':
        this.currentScene = new WelcomeScene(this.game)
        break
      case 'levelSelect':
        this.currentScene = new LevelSelectScene(this.game)
        break
      case 'game':
        this.currentScene = new GameScene(this.game)
        break
      case 'success':
        this.currentScene = new SuccessScene(this.game, params)
        break
      case 'fail':
        this.currentScene = new FailScene(this.game, params)
        break
      default:
        console.error('未知场景:', sceneName)
        return
    }
    
    // 初始化新场景
    console.log('初始化新场景')
    this.currentScene.init()
  }
  
  update() {
    if (this.currentScene) {
      this.currentScene.update()
    }
  }
  
  render() {
    if (this.currentScene) {
      this.currentScene.render()
    }
  }
} 