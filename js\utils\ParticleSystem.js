// 粒子系统
export default class ParticleSystem {
  constructor() {
    this.particles = []
    this.active = false
  }
  
  // 创建粒子爆炸效果
  createExplosion(x, y, color, count = 50, speed = 5, size = 5, duration = 1000) {
    this.active = true
    this.particles = []
    
    for (let i = 0; i < count; i++) {
      // 随机角度
      const angle = Math.random() * Math.PI * 2
      // 随机速度
      const velocity = Math.random() * speed
      // 随机大小
      const particleSize = Math.random() * size + 2
      // 随机寿命
      const life = Math.random() * duration + 500
      
      this.particles.push({
        x,
        y,
        vx: Math.cos(angle) * velocity,
        vy: Math.sin(angle) * velocity,
        size: particleSize,
        color,
        alpha: 1,
        life,
        maxLife: life,
        rotation: Math.random() * 360,
        rotationSpeed: (Math.random() - 0.5) * 10
      })
    }
    
    return this
  }
  
  // 更新粒子
  update(deltaTime) {
    if (!this.active) return
    
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const p = this.particles[i]
      
      // 更新位置
      p.x += p.vx
      p.y += p.vy
      
      // 添加重力效果
      p.vy += 0.1
      
      // 更新旋转
      p.rotation += p.rotationSpeed
      
      // 更新透明度
      p.life -= deltaTime
      p.alpha = p.life / p.maxLife
      
      // 移除死亡粒子
      if (p.life <= 0) {
        this.particles.splice(i, 1)
      }
    }
    
    // 如果没有粒子，停止系统
    if (this.particles.length === 0) {
      this.active = false
    }
  }
  
  // 渲染粒子
  render(ctx) {
    if (!this.active) return
    
    ctx.save()
    
    for (const p of this.particles) {
      ctx.globalAlpha = p.alpha
      ctx.fillStyle = p.color
      
      ctx.translate(p.x, p.y)
      ctx.rotate(p.rotation * Math.PI / 180)
      
      // 绘制粒子（星形）
      ctx.beginPath()
      for (let i = 0; i < 5; i++) {
        const angle = (i * 2 * Math.PI / 5) - Math.PI / 2
        const x = Math.cos(angle) * p.size
        const y = Math.sin(angle) * p.size
        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }
      ctx.closePath()
      ctx.fill()
      
      ctx.setTransform(1, 0, 0, 1, 0, 0)
    }
    
    ctx.restore()
  }
  
  // 检查是否活跃
  isActive() {
    return this.active
  }
} 