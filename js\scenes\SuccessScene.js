import BaseScene from './BaseScene'
import AnimationManager from '../utils/AnimationManager'

export default class SuccessScene extends BaseScene {
  constructor(game, params) {
    super(game)
    this.score = params.score || 0
    this.total = params.total || 0
    this.stars = params.stars || 0
    this.levelId = params.levelId || 1
    this.dataManager = game.dataManager
    this.newlyUnlocked = params.newlyUnlocked || false
    this.nextLevel = params.nextLevel || null
    
    // 解锁提示动画状态
    this.unlockAnimation = {
      alpha: 0,
      scale: 0,
      y: this.height * 0.35
    }
    
    // 添加动画管理器
    this.animationManager = new AnimationManager()
    
    // 检测屏幕方向
    this.isLandscape = true
    
    // 计算按钮布局
    this.calculateButtonLayout()
    
    // 如果有新解锁的关卡，添加动画
    if (this.newlyUnlocked && this.nextLevel) {
      setTimeout(() => {
        // 淡入动画
        this.animationManager.addNumberChange(
          this.unlockAnimation,
          'alpha',
          1,
          500,
          'easeOutQuad'
        )
        
        // 缩放动画
        this.animationManager.addScale(
          this.unlockAnimation,
          0,
          1,
          500,
          'easeOutBack'
        )
      }, 1000)
    }

    // 加载背景图片
    this.bgImage = wx.createImage()
    this.bgImage.src = 'images/bg4.png'
  }
  
  calculateButtonLayout() {
    if (this.isLandscape) {
      // 横屏模式下的按钮布局
      this.buttonWidth = 160  // 减小按钮宽度
      this.buttonHeight = 50  // 减小按钮高度
      this.buttonGap = 20    // 减小按钮间距
      
      // 计算按钮 Y 位置 - 确保在遮罩层内
      const maskHeight = this.height * 0.7
      const maskY = (this.height - maskHeight) / 2
      this.buttonY = maskY + maskHeight * 0.8 // 位于遮罩层底部区域
      
      // 计算按钮 X 位置
      const totalWidth = this.buttonWidth * 3 + this.buttonGap * 2
      const startX = (this.width - totalWidth) / 2
      
      this.levelBtnX = startX
      this.nextBtnX = startX + this.buttonWidth + this.buttonGap
      this.restartBtnX = startX + (this.buttonWidth + this.buttonGap) * 2
    } else {
      // 竖屏模式下的按钮布局
      this.buttonWidth = 180  // 减小按钮宽度
      this.buttonHeight = 50  // 减小按钮高度
      
      // 计算遮罩层位置
      const maskHeight = this.height * 0.7
      const maskY = (this.height - maskHeight) / 2
      
      // 调整按钮垂直位置，确保在遮罩层内
      this.buttonX = this.width / 2 - this.buttonWidth / 2
      this.levelBtnY = maskY + maskHeight * 0.7 // 位于遮罩层下部
      this.nextBtnY = maskY + maskHeight * 0.8 // 位于遮罩层下部
      this.restartBtnY = maskY + maskHeight * 0.9 // 位于遮罩层下部
    }
  }
  
  init() {
    wx.ad.showCha()
    
    // 初始化触摸事件
    this.bindTouchEvents()
    
    // 如果有新解锁的关卡，播放解锁动画
    if (this.newlyUnlocked && this.nextLevel) {
      // 初始化解锁动画
      this.unlockAnimation = {
        alpha: 0,
        scale: 0,
        y: this.height * 0.35
      }
      
      setTimeout(() => {
        // 淡入动画
        this.animationManager.addNumberChange(
          this.unlockAnimation,
          'alpha',
          1,
          500,
          'easeOutQuad'
        )
        
        // 缩放动画
        this.animationManager.addScale(
          this.unlockAnimation,
          0,
          1,
          500,
          'easeOutBack'
        )
      }, 1000)
    }
  }
  
  bindTouchEvents() {
    this.touchHandler = this.onTouchStart.bind(this)
    wx.onTouchStart(this.touchHandler)
  }
  
  onTouchStart(e) {
    const touch = e.touches[0]
    const x = touch.clientX
    const y = touch.clientY
    
    // 如果有解锁提示显示，点击任意位置关闭
    if (this.newlyUnlocked && this.nextLevel && this.unlockAnimation.alpha > 0) {
        // 关闭解锁提示
        this.animationManager.addNumberChange(
            this.unlockAnimation,
            'alpha',
            0,
            300,
            'easeInQuad'
        )
        // 同时缩小动画
        this.animationManager.addScale(
            this.unlockAnimation,
            1,
            0,
            300,
            'easeInQuad'
        )
        return; // 当关闭解锁提示时，不处理其他点击事件
    }

    if (this.isLandscape) {
      // 横屏模式下的按钮点击检测
      if (y >= this.buttonY && y <= this.buttonY + this.buttonHeight) {
        if (this.levelId < this.dataManager.getAllLevels().length) {
          // 检测三个按钮
          if (x >= this.levelBtnX && x <= this.levelBtnX + this.buttonWidth) {
            this.game.sceneManager.showScene('levelSelect')
          } else if (x >= this.nextBtnX && x <= this.nextBtnX + this.buttonWidth) {
            this.dataManager.setCurrentLevel(this.levelId)
            this.game.startGame()
          } else if (x >= this.restartBtnX && x <= this.restartBtnX + this.buttonWidth) {
            this.game.startGame()
          }
        } else {
          // 检测两个按钮
          const totalWidth = this.buttonWidth * 2 + this.buttonGap
          const startX = (this.width - totalWidth) / 2
          
          if (x >= startX && x <= startX + this.buttonWidth) {
            this.game.sceneManager.showScene('levelSelect')
          } else if (x >= startX + this.buttonWidth + this.buttonGap && 
                     x <= startX + this.buttonWidth * 2 + this.buttonGap) {
            this.game.startGame()
          }
        }
      }
    } else {
      // 检测是否点击了关卡选择按钮
      const levelBtnWidth = 200
      const levelBtnHeight = 60
      const levelBtnX = this.width / 2 - levelBtnWidth / 2
      const levelBtnY = this.height * 0.6
      
      if (x >= levelBtnX && x <= levelBtnX + levelBtnWidth && 
          y >= levelBtnY && y <= levelBtnY + levelBtnHeight) {
        console.log('关卡选择按钮被点击')
        this.game.sceneManager.showScene('levelSelect')
      }
      
      // 检测是否点击了下一关按钮
      const nextBtnWidth = 200
      const nextBtnHeight = 60
      const nextBtnX = this.width / 2 - nextBtnWidth / 2
      const nextBtnY = this.height * 0.7
      
      // 只有当有下一关时才检测点击
      const hasNextLevel = this.levelId < this.dataManager.getAllLevels().length
      if (hasNextLevel) {
        if (x >= nextBtnX && x <= nextBtnX + nextBtnWidth && 
            y >= nextBtnY && y <= nextBtnY + nextBtnHeight) {
          console.log('下一关按钮被点击')
          
          // 设置下一关
          this.dataManager.setCurrentLevel(this.levelId)
          
          // 开始游戏
          this.game.startGame()
        }
      }
      
      // 检测是否点击了重新开始按钮
      const restartBtnWidth = 200
      const restartBtnHeight = 60
      const restartBtnX = this.width / 2 - restartBtnWidth / 2
      const restartBtnY = hasNextLevel ? this.height * 0.8 : this.height * 0.7
      
      if (x >= restartBtnX && x <= restartBtnX + restartBtnWidth && 
          y >= restartBtnY && y <= restartBtnY + restartBtnHeight) {
        console.log('重新开始按钮被点击')
        
        // 重新开始当前关卡
        this.game.startGame()
      }
    }
  }
  
  drawStars(x, y, count) {
    const starSize = 40  // 增大星星尺寸
    const starGap = 10   // 增加星星间距
    const totalWidth = (starSize + starGap) * 3 - starGap  // 计算总宽度
    const startX = x - totalWidth / 2  // 居中起始位置
    
    for (let i = 0; i < 3; i++) {
      const starX = startX + i * (starSize + starGap)
      const color = i < count ? '#FFD700' : '#DDDDDD'  // 金色或灰色
      
      // 绘制星星
      this.ctx.fillStyle = color
      this.ctx.font = `${starSize}px Arial`
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('★', starX + starSize/2, y)
    }
  }
  
  render() {
    super.render()
  
    // 绘制背景图片
    if (this.bgImage) {
      // 计算缩放比例以覆盖整个画布
      const scale = Math.max(
        this.width / this.bgImage.width,
        this.height / this.bgImage.height
      )
      
      // 计算绘制尺寸
      const drawWidth = this.bgImage.width * scale
      const drawHeight = this.bgImage.height * scale
      
      // 计算居中位置
      const x = (this.width - drawWidth) / 2
      const y = (this.height - drawHeight) / 2
      
      // 绘制背景图片
      this.ctx.drawImage(
        this.bgImage,
        x, y,
        drawWidth, drawHeight
      )
    }

    // 添加白色遮罩层
    const maskWidth = this.isLandscape ? this.width * 0.6 : this.width * 0.9 // 横屏60%宽度,竖屏90%宽度
    const maskHeight = this.height * 0.7 // 高度为屏幕高度的70%
    const maskX = (this.width - maskWidth) / 2 // 水平居中
    const maskY = (this.height - maskHeight) / 2 // 垂直居中

    // 绘制白色遮罩层背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)' // 半透明白色
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    this.ctx.shadowBlur = 10
    this.ctx.shadowOffsetY = 2
    this.drawRoundRect(
      maskX,
      maskY,
      maskWidth,
      maskHeight,
      20 // 圆角半径
    )

    // 重置阴影
    this.ctx.shadowColor = 'transparent'
    this.ctx.shadowBlur = 0
    this.ctx.shadowOffsetY = 0

    // 绘制成功图标
    const iconSize = this.isLandscape ? 80 : 100
    const iconY = maskY + maskHeight * 0.15 // 调整到遮罩层顶部区域

    this.ctx.font = `${iconSize}px Arial`
    this.ctx.fillStyle = '#4CAF50'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('🎉', this.width / 2, iconY)

    // 绘制成功标题
    const titleY = iconY + iconSize * 0.8
    this.drawText(
      '挑战成功',
      this.width / 2,
      titleY,
      this.config.fonts.title,
      '#4CAF50',
      'center'
    )
    
    // 绘制星级文字
    const starTextY = titleY + 50
    this.drawText(
      '获得星级:',
      this.width / 2,
      starTextY,
      this.config.fonts.body,
      this.config.colors.dark,
      'center'
    )
    
    // 绘制星星
    const starsY = starTextY + 40
    this.drawStars(
      this.width / 2,
      starsY,
      this.stars
    )
    
    // 绘制得分
    const scoreY = starsY + 50
    // this.drawText(
    //   `得分: ${this.score}/${this.total}`,
    //   this.width / 2,
    //   scoreY,
    //   this.config.fonts.subtitle,
    //   this.config.colors.dark,
    //   'center'
    // )
    
    // 绘制评价
    let evaluation = ''
    const percentage = (this.score / this.total) * 100
    
    if (percentage === 100) {
      evaluation = '完美！你是答题大师！'
    } else if (percentage >= 80) {
      evaluation = '太棒了！你的知识面很广！'
    } else {
      evaluation = '不错！继续加油！'
    }
    
    const evaluationY = scoreY + 40
    // this.drawText(
    //   evaluation,
    //   this.width / 2,
    //   evaluationY,
    //   this.config.fonts.body,
    //   '#555555',
    //   'center'
    // )
    
    // 检查是否有下一关
    const hasNextLevel = this.levelId < this.dataManager.getAllLevels().length
    
    if (hasNextLevel) {
      // 获取下一关信息
      const nextLevel = this.dataManager.getAllLevels()[this.levelId]
      
      // 绘制下一关信息
      const nextLevelY = evaluationY + 40
      // this.drawText(
      //   `下一关: ${nextLevel.name}`,
      //   this.width / 2,
      //   nextLevelY,
      //   this.config.fonts.body,
      //   this.config.colors.dark,
      //   'center'
      // )
      
      // 绘制下一关通关要求
      const requirementY = nextLevelY + 30
      this.drawText(
        `通关要求: 答对${nextLevel.passingScore}题`,
        this.width / 2,
        requirementY,
        this.config.fonts.small,
        '#666666',
        'center'
      )
      
      if (this.isLandscape) {
        // 横屏模式下并排显示按钮
        this.drawButton(
          '关卡选择',
          this.levelBtnX,
          this.buttonY,
          this.buttonWidth,
          this.buttonHeight,
          this.config.colors.secondary,
          '#ffffff',
          this.config.fonts.subtitle
        )
        
        this.drawButton(
          '下一关',
          this.nextBtnX,
          this.buttonY,
          this.buttonWidth,
          this.buttonHeight,
          this.config.colors.primary,
          '#ffffff',
          this.config.fonts.subtitle
        )
        
        this.drawButton(
          '再来一次',
          this.restartBtnX,
          this.buttonY,
          this.buttonWidth,
          this.buttonHeight,
          this.config.colors.info,
          '#ffffff',
          this.config.fonts.subtitle
        )
      } else {
        // 保持原有的竖屏布局代码
        // ...现有竖屏模式代码...
      }
    } else {
      // 没有下一关时的布局
      if (this.isLandscape) {
        // 横屏模式下显示两个按钮
        const totalWidth = this.buttonWidth * 2 + this.buttonGap
        const startX = (this.width - totalWidth) / 2
        
        this.drawButton(
          '关卡选择',
          startX,
          this.buttonY,
          this.buttonWidth,
          this.buttonHeight,
          this.config.colors.secondary,
          '#ffffff',
          this.config.fonts.subtitle
        )
        
        this.drawButton(
          '再来一次',
          startX + this.buttonWidth + this.buttonGap,
          this.buttonY,
          this.buttonWidth,
          this.buttonHeight,
          this.config.colors.primary,
          '#ffffff',
          this.config.fonts.subtitle
        )
      } else {
        // 保持原有的竖屏布局代码
        // ...现有竖屏模式代码...
      }
    }
    
    // 如果有新解锁的关卡，显示解锁提示
    if (this.newlyUnlocked && this.nextLevel && this.unlockAnimation.alpha > 0) {
      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
      this.ctx.globalAlpha = this.unlockAnimation.alpha * 0.7
      this.ctx.fillRect(0, 0, this.width, this.height)
      this.ctx.globalAlpha = 1
      
      // 绘制解锁提示框
      const boxWidth = this.width * 0.8
      const boxHeight = this.height * 0.45
      const boxX = this.width / 2 - boxWidth / 2
      const boxY = this.height * 0.35
      
      this.ctx.save()
      this.ctx.globalAlpha = this.unlockAnimation.alpha
      this.ctx.translate(this.width / 2, boxY + boxHeight / 2)
      this.ctx.scale(this.unlockAnimation.scale, this.unlockAnimation.scale)
      this.ctx.translate(-this.width / 2, -(boxY + boxHeight / 2))
      
      // 绘制提示框背景
      this.drawRoundRect(boxX, boxY, boxWidth, boxHeight, 10, '#FFFFFF')
      
      // 绘制解锁图标
      this.ctx.font = '40px Arial'
      this.ctx.fillStyle = '#FFD700'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('🔓', this.width / 2, boxY + 50)
      
      // 绘制解锁文本
      this.drawText(
        '新关卡解锁!',
        this.width / 2,
        boxY + 90,
        this.config.fonts.subtitle,
        this.config.colors.primary,
        'center'
      )
      
      // 绘制关卡名称
      this.drawText(
        this.nextLevel.name,
        this.width / 2,
        boxY + 130,
        this.config.fonts.body,
        this.config.colors.dark,
        'center'
      )
      
      // 添加提示文本
      this.drawText(
        '点击任意位置关闭',
        this.width / 2,
        boxY + 170,
        this.config.fonts.small,
        '#666666',
        'center'
      )
      
      this.ctx.restore()
    }
  }
  
  destroy() {
    // 清理事件
    wx.offTouchStart(this.touchHandler)
  }
  
  update() {
    // 更新动画
    this.animationManager.update()
  }
} 