import BaseScene from './BaseScene'

export default class FailScene extends BaseScene {
  constructor(game, params = {}) {
    super(game)

    // 获取参数
    this.score = params.score || 0
    this.total = params.total || 0
    this.levelId = params.levelId || 0

    // 检测屏幕方向
    this.isLandscape = true

    // 计算按钮布局
    this.calculateButtonLayout()


    // 加载背景图片
    this.bgImage = wx.createImage()
    this.bgImage.src = 'images/bg4.png'
  }

  // 计算按钮布局
  calculateButtonLayout() {
    // 根据屏幕方向计算按钮尺寸和位置
    if (this.isLandscape) {
      // 横屏模式下，按钮并排显示
      this.buttonWidth = 180
      this.buttonHeight = 60
      this.buttonGap = 30

      // 计算按钮位置
      this.buttonY = this.height * 0.7

      // 计算第一个按钮的 X 坐标
      const totalWidth = this.buttonWidth * 2 + this.buttonGap
      this.firstButtonX = (this.width - totalWidth) / 2

      // 计算第二个按钮的 X 坐标
      this.secondButtonX = this.firstButtonX + this.buttonWidth + this.buttonGap
    } else {
      // 竖屏模式下，按钮垂直排列
      this.buttonWidth = 220
      this.buttonHeight = 60
      this.buttonGap = 20

      // 计算按钮位置
      this.firstButtonY = this.height * 0.65
      this.secondButtonY = this.firstButtonY + this.buttonHeight + this.buttonGap

      // 计算按钮的 X 坐标（居中）
      this.buttonX = (this.width - this.buttonWidth) / 2
    }
  }

  init() {
    wx.ad.showCha()
    // 绑定触摸事件
    this.bindTouchEvents()
  }

  bindTouchEvents() {
    this.touchHandler = this.onTouchStart.bind(this)
    wx.onTouchStart(this.touchHandler)
  }

  onTouchStart(e) {
    const touch = e.touches[0]
    const x = touch.clientX
    const y = touch.clientY

    // 检查是否点击了重试按钮
    if (this.isLandscape) {
      // 横屏模式下的按钮位置
      if (x >= this.firstButtonX && x <= this.firstButtonX + this.buttonWidth &&
        y >= this.buttonY && y <= this.buttonY + this.buttonHeight) {
        console.log('重试按钮被点击')

        if (wx.ad._shen_he()) {
          this.game.startGame()
        } else {
          wx.ad.showVideo(() => {
            this.game.startGame()
          })
        }

      }

      // 检查是否点击了返回主页按钮
      if (x >= this.secondButtonX && x <= this.secondButtonX + this.buttonWidth &&
        y >= this.buttonY && y <= this.buttonY + this.buttonHeight) {
        console.log('返回主页按钮被点击')
        this.game.sceneManager.showScene('welcome')
      }
    } else {
      // 竖屏模式下的按钮位置
      if (x >= this.buttonX && x <= this.buttonX + this.buttonWidth &&
        y >= this.firstButtonY && y <= this.firstButtonY + this.buttonHeight) {
        console.log('重试按钮被点击')
        this.game.startGame()
      }

      // 检查是否点击了返回主页按钮
      if (x >= this.buttonX && x <= this.buttonX + this.buttonWidth &&
        y >= this.secondButtonY && y <= this.secondButtonY + this.buttonHeight) {
        console.log('返回主页按钮被点击')
        this.game.sceneManager.showScene('welcome')
      }
    }
  }

  render() {
    super.render()

    // 绘制背景图片
    if (this.bgImage) {
      // 计算缩放比例以覆盖整个画布
      const scale = Math.max(
        this.width / this.bgImage.width,
        this.height / this.bgImage.height
      )

      // 计算绘制尺寸
      const drawWidth = this.bgImage.width * scale
      const drawHeight = this.bgImage.height * scale

      // 计算居中位置
      const x = (this.width - drawWidth) / 2
      const y = (this.height - drawHeight) / 2

      // 绘制背景图片
      this.ctx.drawImage(
        this.bgImage,
        x, y,
        drawWidth, drawHeight
      )
    }

    // 调整白色遮罩层尺寸和位置
    const maskWidth = this.isLandscape ? this.width * 0.6 : this.width * 0.9 // 横屏60%宽度,竖屏90%宽度
    const maskHeight = this.height * 0.7 // 调整高度为屏幕高度的70%
    const maskX = (this.width - maskWidth) / 2 // 水平居中
    const maskY = (this.height - maskHeight) / 2 // 垂直居中

    // 绘制白色遮罩层背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)' // 半透明白色
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    this.ctx.shadowBlur = 10
    this.ctx.shadowOffsetY = 2
    this.drawRoundRect(
      maskX,
      maskY,
      maskWidth,
      maskHeight,
      20 // 增大圆角半径
    )

    // 重置阴影
    this.ctx.shadowColor = 'transparent'
    this.ctx.shadowBlur = 0
    this.ctx.shadowOffsetY = 0

    // 绘制失败图标
    const iconSize = this.isLandscape ? 80 : 100
    const iconY = maskY + maskHeight * 0.2 // 调整图标位置到遮罩层内

    this.ctx.font = `${iconSize}px Arial`
    this.ctx.fillStyle = '#F44336'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('😢', this.width / 2, iconY)

    // 绘制失败标题
    const titleY = iconY + iconSize * 0.8
    this.drawText(
      '挑战失败',
      this.width / 2,
      titleY,
      this.config.fonts.title,
      '#F44336',
      'center'
    )

    // 绘制分数信息
    const scoreY = titleY + 60
    this.drawText(
      `得分: ${this.score}/${this.total}`,
      this.width / 2,
      scoreY,
      this.config.fonts.subtitle,
      '#333333',
      'center'
    )

    // 绘制鼓励信息
    const messageY = scoreY + 40
    // this.drawText(
    //   '再接再厉，继续努力！',
    //   this.width / 2,
    //   messageY,
    //   this.config.fonts.body,
    //   '#666666',
    //   'center'
    // )

    // 根据屏幕方向绘制按钮
    if (this.isLandscape) {
      // 横屏模式下，按钮并排显示

      // 绘制重试按钮
      this.drawButton(
        '再次挑战',
        this.firstButtonX,
        this.buttonY,
        this.buttonWidth,
        this.buttonHeight,
        this.config.colors.primary, // 使用主题色
        '#FFFFFF',
        this.config.fonts.body
      )

      // 绘制返回主页按钮
      this.drawButton(
        '返回主页',
        this.secondButtonX,
        this.buttonY,
        this.buttonWidth,
        this.buttonHeight,
        this.config.colors.secondary, // 使用次要色
        '#FFFFFF',
        this.config.fonts.body
      )
    } else {
      // 竖屏模式下，按钮垂直排列

      // 绘制重试按钮
      this.drawButton(
        '再次挑战',
        this.buttonX,
        this.firstButtonY,
        this.buttonWidth,
        this.buttonHeight,
        this.config.colors.primary, // 使用主题色
        '#FFFFFF',
        this.config.fonts.body
      )

      // 绘制返回主页按钮
      this.drawButton(
        '返回主页',
        this.buttonX,
        this.secondButtonY,
        this.buttonWidth,
        this.buttonHeight,
        this.config.colors.secondary, // 使用次要色
        '#FFFFFF',
        this.config.fonts.body
      )
    }
  }

  destroy() {
    // 清理事件
    wx.offTouchStart(this.touchHandler)
  }
}