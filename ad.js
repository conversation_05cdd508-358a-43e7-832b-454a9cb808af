export default class Ad {
	constructor() {
		this._bannerAd = {}
		this._videoAd = {}
		this._interAd = {}
		var info = wx.getSystemInfoSync();
		console.log(info);
		this.swidth = info.screenWidth
		this.sheight = info.screenHeight
	}

	_shen_he() {
		return false;
	}

	// 加载banner
	_loadBanner(opts) {
		if (('adUnitId' in opts) === false) {
			throw new Error('adUnitId参数缺失')
		}
		if ((opts.adUnitId in this._bannerAd) === false) {
			opts.adIntervals = opts.adIntervals || 30
			const bannerAd = wx.createCustomAd(opts)

			opts.onError = opts.onError || function (err) {
				console.log(opts.adUnitId + '广告加载失败', err)
			}
			bannerAd.onError(opts.onError)
			opts.onLoad = opts.onLoad || function () {
				console.log(opts.adUnitId + '广告加载成功')
			}
			bannerAd.onLoad(opts.onLoad)
			this._bannerAd[opts.adUnitId] = bannerAd
		}
		return this._bannerAd[opts.adUnitId]
	}

	showBanner() {
		if (this._shen_he()) {
			return;
		}
		this._loadBanner({
			// width: 320,
			adUnitId: 'adunit-f2073d879ff30cf6',
			style: {
				width: 320,
				left: (this.swidth - 320) / 2,
				top: this.sheight - 110
			}
		}).show();
	}

	hideBanner() {
		if (this._shen_he()) {
			return;
		}
		this._bannerAd['adunit-f2073d879ff30cf6'].hide()
	}

	showLeft() {
		if (this._shen_he()) {
			return;
		}
		this._loadBanner({
			adUnitId: 'adunit-d81da699b8ace2c8',
			style: {
				left: this.swidth - 61,
				top: (this.sheight - 278) / 3 + 20
			}
		}).show();
	}

	showRight() {
		if (this._shen_he()) {
			return;
		}
		this._loadBanner({
			adUnitId: 'adunit-fe07581609af9262',
			style: {
				left: 3,
				top: (this.sheight - 278) / 3 + 20
			}
		}).show();
	}

	// 显示banner
	_showBanner(opts = {}) {
		this._loadBanner(opts).show()
	}

	// 隐藏banner
	_hideBanner() {
		for (const adUnitId in this._bannerAd) {
			this._bannerAd[adUnitId].hide()
		}
	}

	showVideo(cb) {
		this._showRewardVideo({
			adUnitId: 'adunit-5f13b264fea28710',
			success: () => {
				wx.showToast({
					icon: "success",
					title: '已解锁',
				})
				cb.apply();
			},
			fail: () => {
				wx.showToast({
					icon: "error",
					title: '未完成播放',
				})
			}
		})
	}

	// 加载video
	_loadRewardVideo(opts) {
		if (('adUnitId' in opts) === false) {
			throw new Error('adUnitId参数缺失')
		}
		if ((opts.adUnitId in this._videoAd) === false) {
			const videoAd = wx.createRewardedVideoAd(opts)
			videoAd.offError()
			videoAd.offLoad()
			opts.onError = opts.onError || function (err) {
				console.log(opts.adUnitId + '广告加载失败', err)
			}
			videoAd.onError(opts.onError)
			opts.onLoad = opts.onLoad || function () {
				console.log(opts.adUnitId + '广告加载成功')
			}
			videoAd.onLoad(opts.onLoad)
			this._videoAd[opts.adUnitId] = videoAd
		}
		return this._videoAd[opts.adUnitId]
	}

	// 显示video
	_showRewardVideo(opts) {
		const ad = this._loadRewardVideo(opts)
		ad.offClose()
		ad.onClose(function (res) {
			if ((res && res.isEnded) || res === undefined) {
				opts.success && opts.success()
			} else {
				opts.fail && opts.fail({
					code: -1,
					message: '未完成'
				})
			}
		})
		ad.show().catch(() => {
			ad.load().then(() => {
				ad.show()
			})
		})
	}

	showCha() {
        if (this._shen_he()) {
            return;
        }
		this._loadInterstitialAd({
			adUnitId: 'adunit-8d7fe1dd9325a45b'
		}).show().catch(() => {
			this._loadBanner({
				adUnitId: 'adunit-a1cac5236fbef3d6',
				style: {
					width: 320,
					left: (this.swidth - 320) / 2,
					top: (this.sheight - 388) / 2
				}
			}).show();
		})
	}

	// 加载插屏
	_loadInterstitialAd(opts) {
		if (('adUnitId' in opts) === false) {
			throw new Error('adUnitId参数缺失')
		}
		if ((opts.adUnitId in this._interAd) === false) {
			const interAd = wx.createInterstitialAd(opts)

			opts.onError = opts.onError || function (err) {
				console.log(opts.adUnitId + '广告加载失败', err)
			}
			interAd.onError(opts.onError)
			opts.onLoad = opts.onLoad || function () {
				console.log(opts.adUnitId + '广告加载成功')
			}
			interAd.onLoad(opts.onLoad)
			this._interAd[opts.adUnitId] = interAd
		}
		return this._interAd[opts.adUnitId]
	}

	// 显示插屏
	_showInterstitialAd(opts) {
		this._loadInterstitialAd(opts).show()
	}

}