import BaseScene from './BaseScene'
import ParticleSystem from '../utils/ParticleSystem'
import AnimationManager from '../utils/AnimationManager'

export default class GameScene extends BaseScene {
  constructor(game) {
    super(game)
    this.dataManager = game.dataManager
    
    // 根据困难模式调整答题时间
    const baseTime = game.config.timePerQuestion
    this.remainingTime = game.config.hardMode ? Math.floor(baseTime / 2) : baseTime
    
    this.timer = null
    
    // 添加粒子系统
    this.particleSystem = new ParticleSystem()
    
    // 添加动画管理器
    this.animationManager = new AnimationManager()
    
    // 选项动画状态
    this.optionsState = {
      scale: 1,
      x: 0,
      y: 0,
      shake: false
    }
    
    // 分数显示
    this.displayScore = 0
    
    // 获取当前关卡数据
    this.level = this.game.dataManager.getCurrentLevel()
    this.questions = this.level.questions
    
    // 游戏状态
    this.currentQuestionIndex = 0
    this.score = 0
    this.selectedOption = null
    this.showFeedback = false
    this.feedbackCorrect = false
    
    // 检测屏幕方向
    this.isLandscape = true
    
    // 计算游戏区域尺寸
    this.calculateGameArea()

    // 加载背景图片
    this.bgImage = wx.createImage()
    this.bgImage.src = 'images/bg3.png'
  }
  
  calculateGameArea() {
    // 根据屏幕方向计算游戏区域
    if (this.isLandscape) {
      // 横屏布局
      this.gameAreaWidth = this.width * 0.9
      this.gameAreaHeight = this.height * 0.85
      this.gameAreaX = this.width * 0.05
      this.gameAreaY = this.height * 0.1
      
      // 顶部信息栏
      this.topBarHeight = 60
      this.topBarY = this.gameAreaY
      
      // 问题区域
      this.questionAreaHeight = this.gameAreaHeight * 0.25
      this.questionY = this.topBarY + this.topBarHeight + 20
      
      // 选项区域
      this.optionsStartY = this.questionY + this.questionAreaHeight
      this.optionHeight = 60
      this.optionGap = 15
      
      // 选项布局 - 横屏时可以两列显示
      this.optionsPerRow = 2
      this.optionWidth = (this.gameAreaWidth - 30) / this.optionsPerRow
    } else {
      // 竖屏布局
      this.gameAreaWidth = this.width * 0.95
      this.gameAreaHeight = this.height * 0.9
      this.gameAreaX = this.width * 0.025
      this.gameAreaY = this.height * 0.05
      
      // 顶部信息栏
      this.topBarHeight = 70
      this.topBarY = this.gameAreaY
      
      // 问题区域
      this.questionAreaHeight = this.gameAreaHeight * 0.2
      this.questionY = this.topBarY + this.topBarHeight + 30
      
      // 选项区域
      this.optionsStartY = this.questionY + this.questionAreaHeight
      this.optionHeight = 70
      this.optionGap = 20
      
      // 选项布局 - 竖屏时单列显示
      this.optionsPerRow = 1
      this.optionWidth = this.gameAreaWidth - 20
    }
  }
  
  init() {
    // 初始化触摸事件
    this.bindTouchEvents()
    
    // 开始计时器
    this.startTimer()
    
    // 设置初始分数
    this.displayScore = this.dataManager.gameState.score
  }
  
  bindTouchEvents() {
    this.touchHandler = this.onTouchStart.bind(this)
    wx.onTouchStart(this.touchHandler)
  }
  
  onTouchStart(e) {
    const touch = e.touches[0]
    const x = touch.clientX
    const y = touch.clientY
    
    // 计算顶部信息栏位置，确保与渲染时使用相同的值
    const topBarY = Math.max(this.safeAreaTop - 60, 0)
    
    // 检查是否点击了返回主页按钮
    const homeBtnSize = 40
    const homeBtnX = 20
    const homeBtnY = topBarY + 20  // 修改Y坐标计算
    
    // 使用更精确的点击检测
    if (x >= homeBtnX && x <= homeBtnX + homeBtnSize && 
        y >= homeBtnY && y <= homeBtnY + homeBtnSize) {
        console.log('返回主页按钮被点击')
        // 显示退出确认对话框
        this.showExitConfirmation()
        return
    }
    
    // 如果确认对话框处于活动状态，检查对话框按钮点击
    if (this.confirmDialog && this.confirmDialog.active) {
      // 根据屏幕方向调整对话框尺寸
      const isLandscape = this.width > this.height
      
      // 对话框尺寸
      let boxWidth, boxHeight
      
      if (isLandscape) {
        // 横屏模式下，对话框更宽但高度适中
        boxWidth = this.width * 0.5
        boxHeight = this.height * 0.5  // 增加高度，确保有足够空间
      } else {
        // 竖屏模式下，对话框更窄但高度更大
        boxWidth = this.width * 0.85
        boxHeight = this.height * 0.35  // 增加高度，确保有足够空间
      }
      
      const boxX = this.width / 2 - boxWidth / 2
      const boxY = this.height / 2 - boxHeight / 2
      
      // 绘制按钮容器 - 确保按钮位于对话框底部
      const btnContainerWidth = boxWidth * 0.9
      const btnContainerHeight = 60
      const btnContainerX = boxX + (boxWidth - btnContainerWidth) / 2
      const btnContainerY = boxY + boxHeight - btnContainerHeight - 20  // 距离底部20像素
      
      // 按钮尺寸和间距
      const btnGap = isLandscape ? 20 : 10
      const confirmBtnWidth = (btnContainerWidth - btnGap) / 2
      const confirmBtnHeight = btnContainerHeight
      
      // 确认按钮
      const confirmBtnX = btnContainerX
      const confirmBtnY = btnContainerY
      
      // 取消按钮
      const cancelBtnX = btnContainerX + confirmBtnWidth + btnGap
      const cancelBtnY = btnContainerY
      
      // 检查确认按钮点击
      if (x >= confirmBtnX && x <= confirmBtnX + confirmBtnWidth &&
          y >= confirmBtnY && y <= confirmBtnY + confirmBtnHeight) {
        console.log('确认退出按钮被点击')
        this.confirmExit()
        return
      }
      
      // 检查取消按钮点击
      if (x >= cancelBtnX && x <= cancelBtnX + confirmBtnWidth &&
          y >= cancelBtnY && y <= cancelBtnY + confirmBtnHeight) {
        console.log('取消退出按钮被点击')
        this.closeExitConfirmation()
        return
      }
      
      // 点击对话框外部区域关闭对话框
      this.closeExitConfirmation()
      return
    }
    
    // 如果正在显示反馈，点击任意位置继续
    if (this.showFeedback) {
      this.showFeedback = false
      this.nextQuestion()
      return
    }
    
    // 如果已经回答了问题，不允许再次选择
    if (this.dataManager.gameState.answered) {
      return
    }
    
    // 获取当前问题和选项
    const currentQuestion = this.questions[this.currentQuestionIndex]
    const options = currentQuestion.options
    
    // 计算选项区域的位置
    const questionY = topBarY + 100
    const optionsStartY = questionY + 80
    
    // 横屏模式下使用两列布局
    const isLandscape = this.width > this.height
    const optionsPerRow = isLandscape ? 2 : 1
    
    // 计算选项尺寸和间距
    const optionGap = 15
    const optionHeight = 60
    let optionWidth
    
    if (isLandscape) {
      // 横屏模式下，两列布局
      optionWidth = (this.width * 0.9 - optionGap * 3) / 2
    } else {
      // 竖屏模式下，单列布局
      optionWidth = this.width * 0.8
    }
    
    // 计算起始X坐标
    const startX = (this.width - (optionWidth * optionsPerRow + optionGap * (optionsPerRow - 1))) / 2
    
    // 检查是否点击了选项
    for (let i = 0; i < options.length; i++) {
      const row = Math.floor(i / optionsPerRow)
      const col = i % optionsPerRow
      
      const optionX = startX + col * (optionWidth + optionGap)
      const optionY = optionsStartY + row * (optionHeight + optionGap)
      
      if (x >= optionX && x <= optionX + optionWidth &&
          y >= optionY && y <= optionY + optionHeight) {
        console.log(`选择了选项 ${i}: ${options[i]}`)
        this.selectOption(i)
        break
      }
    }
  }
  
  startTimer() {
    // 重置剩余时间
    const baseTime = this.game.config.timePerQuestion
    this.remainingTime = this.game.config.hardMode ? Math.floor(baseTime / 2) : baseTime
    
    // 清除之前的计时器
    if (this.timer) {
      clearInterval(this.timer)
    }
    
    // 每秒更新一次时间
    this.timer = setInterval(() => {
      this.remainingTime--
      
      // 时间到，结束游戏
      if (this.remainingTime < 0) {
        clearInterval(this.timer)
        this.timer = null
        
        // 直接跳转到失败页面，不显示正确答案
        this.game.sceneManager.showScene('fail', {
          score: this.score,
          total: this.questions.length,
          levelId: this.dataManager.getCurrentLevel().id
        })
      }
    }, 1000)
  }
  
  selectOption(index) {
    // 防止重复选择
    if (this.dataManager.gameState.answered) return
    
    console.log(`选择选项: ${index}`)
    
    // 记录选择的选项
    this.selectedOption = index
    
    // 检查答案
    const currentQuestion = this.questions[this.currentQuestionIndex]
    
    // 确保正确索引存在
    let correctIndex = currentQuestion.correctIndex;
    
    // 如果没有明确的 correctIndex，尝试从 answer 属性获取
    if (correctIndex === undefined && currentQuestion.answer !== undefined) {
        correctIndex = currentQuestion.answer;
    }
    
    // 如果仍然没有找到正确索引，尝试从 correctAnswer 属性获取
    if (correctIndex === undefined && currentQuestion.correctAnswer !== undefined) {
        correctIndex = currentQuestion.options.findIndex(option => 
            option === correctAnswerText || 
            option.toString() === correctAnswerText.toString()
        );
    }
    
    // 如果所有尝试都失败，记录错误并默认第一个选项为正确答案
    if (correctIndex === undefined || correctIndex < 0) {
        console.error('无法确定正确答案索引，默认使用第一个选项', currentQuestion);
        correctIndex = 0;
        currentQuestion.correctIndex = correctIndex;
    }
    
    // 判断是否正确
    const isCorrect = index === correctIndex;
    
    console.log(`选择的选项: ${index}, 正确答案索引: ${correctIndex}, 是否正确: ${isCorrect}`);
    
    if (isCorrect) {
        this.score++;
        this.feedbackCorrect = true;
        this.dataManager.gameState.score = this.score;
    } else {
        this.feedbackCorrect = false;
        // 答错时立即结束游戏
        this.showFeedback = true;
        
        // 停止计时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        // 显示错误效果
        this.showWrongEffect(index);
        
        // 延迟一小段时间后跳转到失败页面
        setTimeout(() => {
            // 跳转到失败场景
            this.game.sceneManager.showScene('fail', {
                score: this.score,
                total: this.questions.length,
                levelId: this.dataManager.getCurrentLevel().id
            });
        }, 1000); // 等待1秒让玩家看到错误反馈
        
        return; // 直接返回，不执行后续代码
    }
    
    // 保存正确答案索引到问题对象，以便渲染时使用
    currentQuestion.correctIndex = correctIndex;
    
    // 标记为已回答
    this.dataManager.gameState.answered = true;
    this.dataManager.gameState.selectedOption = index;
    
    // 重置动画状态
    this.selectedOptionState = {
      index: index,
      scale: 1,
      alpha: 1
    }
    
    // 添加视觉反馈
    if (isCorrect) {
      // 正确答案效果
      this.showCorrectEffect(index)
    } else {
      // 错误答案效果
      this.showWrongEffect(index)
    }
    
    // 显示反馈
    this.showFeedback = true;
    
    // 延迟显示下一题或结果
    setTimeout(() => {
      console.log('准备进入下一题');
      if (!this.showFeedback) {
        // 如果用户已经点击跳过了反馈，不再执行
        return;
      }
      this.showFeedback = false;
      this.nextQuestion();
    }, 2000); // 增加延迟时间，让用户看清结果和动画
  }
  
  // 显示正确答案效果
  showCorrectEffect(index) {
    // 初始化 selectedOptionState，避免 null 引用
    if (!this.selectedOptionState) {
      this.selectedOptionState = {
        index: index,
        scale: 1,
        alpha: 1
      }
    } else {
      this.selectedOptionState.index = index
      this.selectedOptionState.scale = 1
      this.selectedOptionState.alpha = 1
    }
    
    // 计算选项位置
    const topBarY = Math.max(this.safeAreaTop - 60, 0)
    const questionY = topBarY + 100
    const optionsStartY = questionY + 80
    
    // 横屏模式下使用两列布局
    const isLandscape = this.width > this.height
    const optionsPerRow = isLandscape ? 2 : 1
    
    // 计算选项尺寸和间距
    const optionGap = 15
    const optionHeight = 60
    let optionWidth
    
    if (isLandscape) {
      // 横屏模式下，两列布局
      optionWidth = (this.width * 0.9 - optionGap * 3) / 2
    } else {
      // 竖屏模式下，单列布局
      optionWidth = this.width * 0.8
    }
    
    // 计算起始X坐标
    const startX = (this.width - (optionWidth * optionsPerRow + optionGap * (optionsPerRow - 1))) / 2
    
    // 计算选项位置
    const row = Math.floor(index / optionsPerRow)
    const col = index % optionsPerRow
    
    const optionX = startX + col * (optionWidth + optionGap)
    const optionY = optionsStartY + row * (optionHeight + optionGap)
    
    // 创建粒子爆炸
    this.particleSystem.createExplosion(
      optionX + optionWidth / 2, 
      optionY + optionHeight / 2, 
      this.config.colors.success,
      80,  // 增加粒子数量
      2,   // 增加粒子大小
      1.5  // 增加粒子速度
    )
    
    // 创建正确答案消息
    this.correctMessage = {
      text: '正确!',
      x: optionX + optionWidth / 2,
      y: optionY - 20,
      alpha: 0,
      scale: 0
    }
    
    // 创建动画管理器（如果不存在）
    if (!this.animationManager) {
      this.animationManager = new AnimationManager()
    }
    
    // 选项缩放动画
    if (this.selectedOptionState) {
      this.animationManager.addScale(
        this.selectedOptionState,
        1,
        1.1,
        200,
        'easeOutQuad',
        () => {
          if (this.selectedOptionState) {  // 再次检查，确保对象仍然存在
            this.animationManager.addScale(
              this.selectedOptionState,
              1.1,
              1,
              200,
              'easeInQuad'
            )
          }
        }
      )
    }
    
    // 添加文字提示动画
    if (this.correctMessage) {
      // 淡入动画
      this.animationManager.addNumberChange(
        this.correctMessage,
        'alpha',
        1,
        300,
        'easeOutQuad'
      )
      
      // 缩放动画
      this.animationManager.addScale(
        this.correctMessage,
        0,
        1,
        300,
        'easeOutBack'
      )
      
      // 上浮动画
      this.animationManager.addNumberChange(
        this.correctMessage,
        'y',
        this.correctMessage.y - 30,
        1000,
        'easeOutQuad'
      )
      
      // 淡出动画
      setTimeout(() => {
        if (this.correctMessage) {  // 确保对象仍然存在
          this.animationManager.addNumberChange(
            this.correctMessage,
            'alpha',
            0,
            500,
            'easeInQuad'
          )
        }
      }, 1000)
    }
    
    // 添加闪光效果
    this.flashEffect = {
      alpha: 0.8,
      color: this.config.colors.success
    }
    
    // 闪光淡出动画
    if (this.flashEffect) {
      this.animationManager.addNumberChange(
        this.flashEffect,
        'alpha',
        0,
        500,
        'easeOutQuad'
      )
    }
  }
  
  // 显示错误答案效果
  showWrongEffect(index) {
    // 初始化 selectedOptionState，避免 null 引用
    if (!this.selectedOptionState) {
      this.selectedOptionState = {
        index: index,
        scale: 1,
        alpha: 1
      }
    } else {
      this.selectedOptionState.index = index
      this.selectedOptionState.scale = 1
      this.selectedOptionState.alpha = 1
    }
    
    // 添加震动效果，但只应用于选项状态
    if (!this.optionsState) {
      this.optionsState = {
        scale: 1,
        x: 0,
        y: 0,
        shake: false
      }
    }
    
    this.optionsState.shake = true
    
    // 创建震动动画
    const shakeAmount = 5
    const shakeDuration = 50
    const shakeCount = 4
    
    // 创建动画管理器（如果不存在）
    if (!this.animationManager) {
      this.animationManager = new AnimationManager()
    }
    
    // 创建震动序列
    for (let i = 0; i < shakeCount; i++) {
      setTimeout(() => {
        if (this.optionsState) {  // 确保对象仍然存在
          // 向右震动
          this.animationManager.addNumberChange(
            this.optionsState,
            'x',
            shakeAmount,
            shakeDuration,
            'easeInOutQuad',
            () => {
              if (this.optionsState) {  // 再次检查，确保对象仍然存在
                // 向左震动
                this.animationManager.addNumberChange(
                  this.optionsState,
                  'x',
                  -shakeAmount,
                  shakeDuration,
                  'easeInOutQuad',
                  () => {
                    if (this.optionsState) {  // 再次检查，确保对象仍然存在
                      // 回到中心
                      this.animationManager.addNumberChange(
                        this.optionsState,
                        'x',
                        0,
                        shakeDuration,
                        'easeInOutQuad',
                        i === shakeCount - 1 ? () => {
                          if (this.optionsState) {
                            this.optionsState.shake = false
                          }
                        } : null
                      )
                    }
                  }
                )
              }
            }
          )
        }
      }, i * shakeDuration * 3)
    }
    
    // 创建错误答案消息
    this.wrongMessage = {
      text: '错误!',
      x: this.width / 2,
      y: this.height * 0.4,
      alpha: 0,
      scale: 0
    }
    
    // 添加文字提示动画
    if (this.wrongMessage) {
      // 淡入动画
      this.animationManager.addNumberChange(
        this.wrongMessage,
        'alpha',
        1,
        300,
        'easeOutQuad'
      )
      
      // 缩放动画
      this.animationManager.addScale(
        this.wrongMessage,
        0,
        1,
        300,
        'easeOutBack'
      )
      
      // 淡出动画
      setTimeout(() => {
        if (this.wrongMessage) {  // 确保对象仍然存在
          this.animationManager.addNumberChange(
            this.wrongMessage,
            'alpha',
            0,
            500,
            'easeInQuad'
          )
        }
      }, 1000)
    }
    
    // 添加闪光效果
    this.flashEffect = {
      alpha: 0.8,
      color: this.config.colors.danger
    }
    
    // 闪光淡出动画
    if (this.flashEffect) {
      this.animationManager.addNumberChange(
        this.flashEffect,
        'alpha',
        0,
        500,
        'easeOutQuad'
      )
    }
  }
  
  // 添加显示时间到效果的方法
  showTimeUpEffect() {
    // 创建时间到消息
    this.timeUpMessage = {
      text: '时间到!',
      x: this.width / 2,
      y: this.height * 0.4,
      alpha: 0,
      scale: 0
    }
    
    // 添加文字提示动画
    if (this.timeUpMessage) {
      // 淡入动画
      this.animationManager.addNumberChange(
        this.timeUpMessage,
        'alpha',
        1,
        300,
        'easeOutQuad'
      )
      
      // 缩放动画
      this.animationManager.addScale(
        this.timeUpMessage,
        0,
        1,
        300,
        'easeOutBack'
      )
      
      // 淡出动画
      setTimeout(() => {
        if (this.timeUpMessage) {
          this.animationManager.addNumberChange(
            this.timeUpMessage,
            'alpha',
            0,
            500,
            'easeInQuad'
          )
        }
      }, 500)
    }
    
    // 添加闪光效果
    this.flashEffect = {
      alpha: 0.8,
      color: this.config.colors.danger
    }
    
    // 闪光淡出动画
    if (this.flashEffect) {
      this.animationManager.addNumberChange(
        this.flashEffect,
        'alpha',
        0,
        500,
        'easeOutQuad'
      )
    }
  }
  
  nextQuestion() {
    console.log('尝试进入下一题')
    
    // 重置动画状态
    this.selectedOptionState = null;
    this.correctMessage = null;
    this.wrongMessage = null;
    this.flashEffect = null;
    this.optionsState = {
      scale: 1,
      x: 0,
      y: 0,
      shake: false
    };
    
    // 重置选项选择状态
    this.selectedOption = null;
    this.showFeedback = false;
    
    // 增加当前问题索引
    this.currentQuestionIndex++;
    
    // 重置数据管理器中的状态
    this.dataManager.gameState.answered = false;
    this.dataManager.gameState.selectedOption = null;
    
    // 检查是否还有下一题
    if (this.currentQuestionIndex < this.questions.length) {
      console.log(`进入第 ${this.currentQuestionIndex + 1} 题`);
      
      // 更新数据管理器中的当前问题索引
      this.dataManager.gameState.currentQuestionIndex = this.currentQuestionIndex;
      
      // 重新开始计时器
      this.startTimer();
    } else {
      console.log('没有下一题，游戏结束');
      // 游戏结束，显示结果
      const score = this.score;
      const level = this.dataManager.getCurrentLevel();
      const passingScore = level.passingScore;
      const total = this.questions.length;
      
      console.log(`最终得分: ${score}/${total}, 通过分数: ${passingScore}`);
      
      // 检查是否通关
      const isPassed = score >= passingScore;
      
      // 更新星星数量
      const stars = this.dataManager.updateLevelStars();
      
      // 更新关卡状态
      if (isPassed) {
        console.log('游戏通关，显示成功场景');
        
        // 解锁下一关
        const unlockResult = this.dataManager.unlockNextLevel();
        
        this.game.sceneManager.showScene('success', {
          score: score,
          total: total,
          stars: stars,  // 使用计算出的星星数量
          levelId: level.id,
          newlyUnlocked: unlockResult.newlyUnlocked,
          nextLevel: unlockResult.level
        });
      } else {
        console.log('游戏失败，显示失败场景');
        this.game.sceneManager.showScene('fail', {
          score: score,
          total: total,
          levelId: level.id
        });
      }
    }
  }
  
  update() {
    // 更新粒子系统
    this.particleSystem.update(16) // 假设16ms每帧
    
    // 更新动画
    this.animationManager.update()
  }
  
  render() {
    super.render()
    
    // 绘制背景图片
    if (this.bgImage) {
      // 计算缩放比例以覆盖整个画布
      const scale = Math.max(
        this.width / this.bgImage.width,
        this.height / this.bgImage.height
      )
      
      // 计算绘制尺寸
      const drawWidth = this.bgImage.width * scale
      const drawHeight = this.bgImage.height * scale
      
      // 计算居中位置
      const x = (this.width - drawWidth) / 2
      const y = (this.height - drawHeight) / 2
      
      // 绘制背景图片
      this.ctx.drawImage(
        this.bgImage,
        x, y,
        drawWidth, drawHeight
      )
    }

    // 绘制闪烁效果
    if (this.flashEffect && this.flashEffect.alpha > 0) {
      this.ctx.fillStyle = this.flashEffect.color
      this.ctx.globalAlpha = this.flashEffect.alpha
      this.ctx.fillRect(0, 0, this.width, this.height)
      this.ctx.globalAlpha = 1
    }
    
    // 获取当前问题
    const currentQuestion = this.questions[this.currentQuestionIndex]
    const gameState = this.dataManager.gameState
    
    // 计算顶部信息栏位置，确保不被胶囊按钮遮挡
    const topBarY = Math.max(this.safeAreaTop - 60, 0)
    
    // 绘制顶部信息栏背景
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    this.ctx.shadowBlur = 5
    this.ctx.shadowOffsetY = 2
    this.ctx.fillRect(0, 0, this.width, 130)
    this.ctx.shadowBlur = 0
    this.ctx.shadowOffsetY = 0
    
    // 获取关卡信息
    const level = this.dataManager.getCurrentLevel()
    const currentQuestionIndex = this.dataManager.gameState.currentQuestionIndex
    const totalQuestions = this.dataManager.getTotalQuestions()
    const currentScore = this.dataManager.gameState.score
    
    // 绘制关卡名称
    this.drawText(
      level.name,
      this.width / 2,
      topBarY + 20,
      this.config.fonts.body,
      this.config.colors.primary,
      'center'
    )
    
    // 绘制分隔线
    this.ctx.strokeStyle = '#EEEEEE'
    this.ctx.beginPath()
    this.ctx.moveTo(this.width / 2, topBarY + 30)
    this.ctx.lineTo(this.width / 2, topBarY + 55)
    this.ctx.stroke()
    
    // 左侧信息区域
    // 绘制题目进度
    this.drawText(
      `题目: ${currentQuestionIndex + 1}/${totalQuestions}`,
      this.width / 4 - 60,
      topBarY + 45,
      this.config.fonts.small,
      this.config.colors.dark,
      'center'
    )
    
    // 绘制时间
    const timeColor = this.remainingTime <= 5 ? '#FF5722' : this.config.colors.dark
    this.drawText(
      `时间: ${this.remainingTime}秒`,
      this.width / 4 + 60,
      topBarY + 45,
      this.config.fonts.small,
      timeColor,
      'center'
    )
    
    // 右侧信息区域
    // 绘制当前分数
    this.drawText(
      `得分: ${currentScore}`,
      this.width / 2 + 20,
      topBarY + 45,
      this.config.fonts.small,
      this.config.colors.dark,
      'left'
    )
    
    // 绘制通关要求
    const passColor = currentScore >= level.passingScore ? '#4CAF50' : '#FF5722'
    this.drawText(
      `通关: ${level.passingScore}分`,
      this.width - 100,
      topBarY + 45,
      this.config.fonts.small,
      passColor,
      'right'
    )
    
    // 调整问题位置
    const questionY = topBarY + 100
    
    // 绘制问题
    this.drawText(
      currentQuestion.question,
      this.width / 2,
      questionY,
      this.config.fonts.subtitle,
      this.config.colors.dark,
      'center'
    )
    
    // 调整选项位置
    const optionsStartY = questionY + 80
    
    // 横屏模式下使用两列布局
    const isLandscape = this.width > this.height
    const optionsPerRow = isLandscape ? 2 : 1
    
    // 计算选项尺寸和间距
    const optionGap = 15
    const optionHeight = 60
    let optionWidth
    
    if (isLandscape) {
      // 横屏模式下，两列布局
      optionWidth = (this.width * 0.9 - optionGap * 3) / 2
    } else {
      // 竖屏模式下，单列布局
      optionWidth = this.width * 0.8
    }
    
    // 计算起始X坐标
    const startX = (this.width - (optionWidth * optionsPerRow + optionGap * (optionsPerRow - 1))) / 2
    
    // 绘制选项
    for (let i = 0; i < currentQuestion.options.length; i++) {
      const row = Math.floor(i / optionsPerRow)
      const col = i % optionsPerRow
      
      const optionX = startX + col * (optionWidth + optionGap)
      const optionY = optionsStartY + row * (optionHeight + optionGap)
      
      let bgColor = this.config.colors.light // 默认背景色
      
      // 确保正确索引存在
      let correctIndex = currentQuestion.correctIndex;
      
      // 如果没有明确的 correctIndex，尝试从 answer 属性获取
      if (correctIndex === undefined && currentQuestion.answer !== undefined) {
        correctIndex = currentQuestion.answer;
      }
      
      // 如果已经回答，显示正确/错误颜色
      if (gameState.answered) {
        if (i === correctIndex) {
          bgColor = this.config.colors.success // 正确答案使用成功色
        } else if (i === gameState.selectedOption) {
          bgColor = this.config.colors.danger // 错误选择使用危险色
        }
      } else if (i === this.selectedOption) {
        bgColor = this.config.colors.info // 选中但未确认使用信息色
      }
      
      // 默认绘制参数
      let drawX = optionX
      let drawY = optionY
      let drawWidth = optionWidth
      let drawHeight = optionHeight
      let alpha = 1
      
      // 如果是选中的选项且有特殊状态，应用特殊效果
      if (this.selectedOptionState && i === this.selectedOptionState.index) {
        // 计算缩放后的尺寸和位置
        const scaleChange = this.selectedOptionState.scale - 1
        drawWidth = optionWidth * this.selectedOptionState.scale
        drawHeight = optionHeight * this.selectedOptionState.scale
        
        // 居中缩放
        drawX = optionX - (drawWidth - optionWidth) / 2
        drawY = optionY - (drawHeight - optionHeight) / 2
        
        // 应用透明度
        alpha = this.selectedOptionState.alpha
      } else if (this.optionsState && this.optionsState.shake) {
        // 如果有震动效果，应用到所有选项
        drawX += this.optionsState.x
        drawY += this.optionsState.y
      }
      
      // 设置透明度
      this.ctx.globalAlpha = alpha
      
      // 绘制选项背景
      this.drawRoundRect(drawX, drawY, drawWidth, drawHeight, 8, bgColor)
      
      // 绘制选项文本
      this.drawText(
        `${String.fromCharCode(65 + i)}. ${currentQuestion.options[i]}`,
        drawX + 20,
        drawY + drawHeight / 2 + 6,
        this.config.fonts.body,
        this.config.colors.dark,
        'left'
      )
      
      // 重置透明度
      this.ctx.globalAlpha = 1
    }
    
    // 绘制返回主页按钮
    const homeBtnSize = 40
    const homeBtnX = 20
    const homeBtnY = topBarY + 20  // 确保与点击检测使用相同的位置

    // 绘制按钮背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    this.drawRoundRect(
        homeBtnX,
        homeBtnY,
        homeBtnSize,
        homeBtnSize,
        8,
        'rgba(255, 255, 255, 0.8)'
    )

    // 绘制返回图标
    this.ctx.font = '24px Arial'
    this.ctx.fillStyle = '#666666'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(
        '🏠',
        homeBtnX + homeBtnSize/2,
        homeBtnY + homeBtnSize/2
    )
    
    // 如果有确认对话框，绘制它
    if (this.confirmDialog && this.confirmDialog.active && this.confirmDialog.alpha > 0) {
      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
      this.ctx.globalAlpha = this.confirmDialog.alpha * 0.6
      this.ctx.fillRect(0, 0, this.width, this.height)
      this.ctx.globalAlpha = 1
      
      // 根据屏幕方向调整对话框尺寸
      const isLandscape = this.width > this.height
      
      // 对话框尺寸
      let boxWidth, boxHeight
      
      if (isLandscape) {
        // 横屏模式下，对话框更宽但高度适中
        boxWidth = this.width * 0.5
        boxHeight = this.height * 0.5  // 增加高度，确保有足够空间
      } else {
        // 竖屏模式下，对话框更窄但高度更大
        boxWidth = this.width * 0.85
        boxHeight = this.height * 0.35  // 增加高度，确保有足够空间
      }
      
      const boxX = this.width / 2 - boxWidth / 2
      const boxY = this.height / 2 - boxHeight / 2
      
      this.ctx.save()
      this.ctx.globalAlpha = this.confirmDialog.alpha
      this.ctx.translate(this.width / 2, boxY + boxHeight / 2)
      this.ctx.scale(this.confirmDialog.scale, this.confirmDialog.scale)
      this.ctx.translate(-this.width / 2, -(boxY + boxHeight / 2))
      
      // 绘制对话框背景（带阴影）
      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)'
      this.ctx.shadowBlur = 15
      this.ctx.shadowOffsetY = 5
      this.drawRoundRect(boxX, boxY, boxWidth, boxHeight, 15, '#FFFFFF')
      this.ctx.shadowColor = 'transparent'
      
      // 绘制警告图标 - 调整位置
      const iconY = boxY + boxHeight * 0.2
      this.ctx.font = '30px Arial'
      this.ctx.fillStyle = '#FFC107'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('⚠️', this.width / 2, iconY)
      
      // 绘制提示文本 - 调整位置
      const titleY = iconY + 40
      this.drawText(
        '确定要退出游戏吗？',
        this.width / 2,
        titleY,
        this.config.fonts.subtitle,
        this.config.colors.dark,
        'center'
      )
      
      // 绘制提示说明 - 调整位置
      const descY = titleY + 30
      this.drawText(
        '当前进度将不会保存',
        this.width / 2,
        descY,
        this.config.fonts.small,
        '#666666',
        'center'
      )
      
      // 绘制按钮容器 - 确保按钮位于对话框底部
      const btnContainerWidth = boxWidth * 0.9
      const btnContainerHeight = 60
      const btnContainerX = boxX + (boxWidth - btnContainerWidth) / 2
      const btnContainerY = boxY + boxHeight - btnContainerHeight - 20  // 距离底部20像素
      
      // 按钮尺寸和间距
      const btnGap = isLandscape ? 20 : 10
      const confirmBtnWidth = (btnContainerWidth - btnGap) / 2
      const confirmBtnHeight = btnContainerHeight
      
      // 绘制确认按钮（圆角矩形）
      const confirmBtnX = btnContainerX
      const confirmBtnY = btnContainerY
      
      this.drawButton(
        '退出',
        confirmBtnX,
        confirmBtnY,
        confirmBtnWidth,
        confirmBtnHeight,
        this.config.colors.danger,
        '#ffffff',
        this.config.fonts.body
      )
      
      // 绘制取消按钮（圆角矩形）
      const cancelBtnX = btnContainerX + confirmBtnWidth + btnGap
      const cancelBtnY = btnContainerY
      
      this.drawButton(
        '继续游戏',
        cancelBtnX,
        cancelBtnY,
        confirmBtnWidth,
        confirmBtnHeight,
        this.config.colors.primary,
        '#ffffff',
        this.config.fonts.body
      )
      
      this.ctx.restore()
    }
    
    // 渲染粒子系统
    this.particleSystem.render(this.ctx)
  }
  
  destroy() {
    console.log('销毁游戏场景')
    
    // 清理计时器
    if (this.timer) {
      console.log('清理计时器')
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 清理状态
    this.selectedOptionState = null
    this.correctMessage = null
    this.wrongMessage = null
    this.flashEffect = null
    this.optionsState = {
      scale: 1,
      x: 0,
      y: 0,
      shake: false
    }
    
    // 清理事件
    console.log('清理触摸事件')
    wx.offTouchStart(this.touchHandler)
  }
  
  // 添加显示退出确认对话框的方法
  showExitConfirmation() {
    // 创建确认对话框状态
    this.confirmDialog = {
      active: true,
      alpha: 0,
      scale: 0,
      // 添加屏幕方向标记
      isLandscape: this.width > this.height
    }
    
    // 创建动画管理器（如果不存在）
    if (!this.animationManager) {
      this.animationManager = new AnimationManager()
    }
    
    // 确保 confirmDialog 存在
    if (this.confirmDialog) {
      // 淡入动画
      this.animationManager.addNumberChange(
        this.confirmDialog,
        'alpha',
        1,
        200,
        'easeOutQuad'
      )
      
      // 缩放动画
      this.animationManager.addScale(
        this.confirmDialog,
        0,
        1,
        200,
        'easeOutBack'
      )
    }
    
    // 暂停游戏计时器
    this.pauseTimer()
  }
  
  // 添加关闭确认对话框的方法
  closeExitConfirmation() {
    if (this.confirmDialog && this.confirmDialog.active) {
      // 确保动画管理器存在
      if (!this.animationManager) {
        this.animationManager = new AnimationManager()
      }
      
      // 淡出动画
      this.animationManager.addNumberChange(
        this.confirmDialog,
        'alpha',
        0,
        200,
        'easeInQuad',
        () => {
          if (this.confirmDialog) {  // 确保对象仍然存在
            this.confirmDialog.active = false
          }
        }
      )
      
      // 恢复游戏计时器
      this.resumeTimer()
    }
  }
  
  // 添加确认退出的方法
  confirmExit() {
    // 清理计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 返回主页
    this.game.sceneManager.showScene('welcome')
  }
  
  // 添加暂停和恢复计时器的方法
  pauseTimer() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
      this.isPaused = true
      this.pauseTime = Date.now()
    }
  }
  
  resumeTimer() {
    if (this.isPaused) {
      const elapsedPauseTime = Date.now() - this.pauseTime
      this.endTime += elapsedPauseTime
      this.startTimer()
      this.isPaused = false
    }
  }
} 