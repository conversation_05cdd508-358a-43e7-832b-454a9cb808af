import { drawRoundRect, drawText, drawButton } from '../utils/renderer'

export default class BaseScene {
  constructor(game) {
    this.game = game
    this.canvas = game.canvas
    this.ctx = game.ctx
    this.width = this.canvas.width
    this.height = this.canvas.height
    this.config = game.config
    
    // 获取胶囊按钮信息
    this.menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    console.log('胶囊按钮信息:', this.menuButtonInfo)
    
    // 计算安全区域
    this.safeAreaTop = this.menuButtonInfo.bottom + 10 // 胶囊底部加上一些间距
  }
  
  init() {
    // 初始化场景
  }
  
  update() {
    // 更新场景
  }
  
  render() {
    // 渲染场景
    this.ctx.clearRect(0, 0, this.width, this.height)
  }
  
  destroy() {
    // 销毁场景
  }
  
  // 辅助渲染方法
  drawRoundRect(x, y, width, height, radius, color) {
    drawRoundRect(this.ctx, x, y, width, height, radius, color)
  }
  
  drawText(text, x, y, font, color, align) {
    drawText(this.ctx, text, x, y, font, color, align)
  }
  
  drawButton(text, x, y, width, height, color, textColor, font) {
    drawButton(this.ctx, text, x, y, width, height, color, textColor, font)
  }
  
  // 添加屏幕方向变化处理方法
  onScreenOrientationChange(isLandscape) {
    this.isLandscape = isLandscape
    this.width = this.canvas.width
    this.height = this.canvas.height
    
    // 如果场景有特定的布局计算方法，调用它
    if (typeof this.calculateLayout === 'function') {
      this.calculateLayout()
    }
  }
} 