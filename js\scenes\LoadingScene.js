import BaseScene from './BaseScene'

export default class LoadingScene extends BaseScene {
  constructor(game) {
    super(game)
    this.progress = 0
    this.loadingSpeed = 2
    this.resources = [
      'images/bg.png',
      'images/btn.png',
      'images/success.png',
      'images/fail.png'
    ]
    this.loadedCount = 0
  }
  
  init() {
    // 初始化触摸事件
    this.initTouchEvents()
    
    // 加载资源
    this.loadResources()
  }
  
  initTouchEvents() {
    // 微信小游戏中的触摸事件
    wx.onTouchStart(this.onTouchStart.bind(this))
  }
  
  onTouchStart(e) {
    // 加载完成后点击任意位置开始游戏
    if (this.progress >= 100) {
      wx.offTouchStart()
      this.game.showScene('game')
    }
  }
  
  loadResources() {
    // 模拟资源加载
    if (this.resources.length === 0) {
      this.progress = 100
      return
    }
    
    this.resources.forEach(path => {
      const image = wx.createImage()
      image.src = path
      image.onload = () => {
        this.loadedCount++
        this.progress = (this.loadedCount / this.resources.length) * 100
        
        if (this.loadedCount === this.resources.length) {
          console.log('所有资源加载完成')
        }
      }
    })
  }
  
  update() {
    // 模拟加载进度
    if (this.progress < 100) {
      this.progress = Math.min(100, this.progress + this.loadingSpeed * 0.1)
    }
  }
  
  render(ctx) {
    // 绘制背景
    this.drawRect(0, 0, this.width, this.height, '#f5f5f5')
    
    // 绘制标题
    this.drawText('答题挑战', this.width / 2, this.height / 3, 36, '#333')
    
    // 绘制加载进度条背景
    this.drawRect(this.width * 0.2, this.height * 0.5, this.width * 0.6, 20, this.config.colors.light)
    
    // 绘制加载进度条
    this.drawRect(
      this.width * 0.2, 
      this.height * 0.5, 
      this.width * 0.6 * (this.progress / 100), 
      20, 
      this.config.colors.primary // 使用主题色
    )
    
    // 绘制加载进度文本
    this.drawText(`${Math.floor(this.progress)}%`, this.width / 2, this.height * 0.5 + 40, 18, '#666')
    
    // 加载完成后显示提示
    if (this.progress >= 100) {
      this.drawText('点击屏幕开始游戏', this.width / 2, this.height * 0.7, 24, '#333')
    }
  }
  
  destroy() {
    // 清理事件
    wx.offTouchStart()
  }
} 