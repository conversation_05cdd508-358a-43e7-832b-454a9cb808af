/**
 * 动画管理器
 * 用于管理游戏中的各种动画效果
 */
export default class AnimationManager {
  constructor() {
    this.animations = []
    this.lastTime = Date.now()
  }
  
  /**
   * 更新所有动画
   */
  update() {
    const now = Date.now()
    const deltaTime = now - this.lastTime
    this.lastTime = now
    
    // 过滤掉已完成的动画
    this.animations = this.animations.filter(anim => {
      // 更新动画进度
      anim.currentTime += deltaTime
      
      // 计算进度比例
      const progress = Math.min(1, anim.currentTime / anim.duration)
      
      // 应用缓动函数
      const easedProgress = this.applyEasing(progress, anim.easing)
      
      // 根据动画类型更新目标属性
      if (anim.type === 'numberChange') {
        const diff = anim.endValue - anim.startValue
        anim.target[anim.property] = anim.startValue + diff * easedProgress
      } else if (anim.type === 'scale') {
        anim.target.scale = anim.startValue + (anim.endValue - anim.startValue) * easedProgress
      } else if (anim.type === 'move') {
        anim.target.x = anim.startX + (anim.endX - anim.startX) * easedProgress
        anim.target.y = anim.startY + (anim.endY - anim.startY) * easedProgress
      } else if (anim.type === 'shake') {
        if (progress < 1) {
          const intensity = anim.intensity * (1 - progress)
          anim.target.x = Math.random() * intensity * 2 - intensity
          anim.target.y = Math.random() * intensity * 2 - intensity
        } else {
          anim.target.x = 0
          anim.target.y = 0
        }
      }
      
      // 如果动画完成，执行回调
      if (progress >= 1 && anim.onComplete) {
        anim.onComplete()
      }
      
      // 返回是否保留此动画（未完成的保留）
      return progress < 1
    })
  }
  
  /**
   * 添加数值变化动画
   * @param {Object} target 目标对象
   * @param {String} property 要变化的属性
   * @param {Number} endValue 目标值
   * @param {Number} duration 持续时间（毫秒）
   * @param {String} easing 缓动函数名称
   * @param {Function} onComplete 完成回调
   */
  addNumberChange(target, property, endValue, duration = 1000, easing = 'linear', onComplete = null) {
    const startValue = target[property] || 0
    
    this.animations.push({
      type: 'numberChange',
      target,
      property,
      startValue,
      endValue,
      duration,
      currentTime: 0,
      easing,
      onComplete
    })
  }
  
  /**
   * 添加缩放动画
   * @param {Object} target 目标对象
   * @param {Number} startScale 起始缩放值
   * @param {Number} endScale 目标缩放值
   * @param {Number} duration 持续时间（毫秒）
   * @param {String} easing 缓动函数名称
   * @param {Function} onComplete 完成回调
   */
  addScale(target, startScale, endScale, duration = 1000, easing = 'linear', onComplete = null) {
    this.animations.push({
      type: 'scale',
      target,
      startValue: startScale,
      endValue: endScale,
      duration,
      currentTime: 0,
      easing,
      onComplete
    })
  }
  
  /**
   * 添加移动动画
   * @param {Object} target 目标对象
   * @param {Number} startX 起始X坐标
   * @param {Number} startY 起始Y坐标
   * @param {Number} endX 目标X坐标
   * @param {Number} endY 目标Y坐标
   * @param {Number} duration 持续时间（毫秒）
   * @param {String} easing 缓动函数名称
   * @param {Function} onComplete 完成回调
   */
  addMove(target, startX, startY, endX, endY, duration = 1000, easing = 'linear', onComplete = null) {
    this.animations.push({
      type: 'move',
      target,
      startX,
      startY,
      endX,
      endY,
      duration,
      currentTime: 0,
      easing,
      onComplete
    })
  }
  
  /**
   * 添加震动动画
   * @param {Object} target 目标对象
   * @param {Number} intensity 震动强度
   * @param {Number} duration 持续时间（毫秒）
   * @param {Function} onComplete 完成回调
   */
  addShake(target, intensity = 5, duration = 500, onComplete = null) {
    this.animations.push({
      type: 'shake',
      target,
      intensity,
      duration,
      currentTime: 0,
      easing: 'linear',
      onComplete
    })
  }
  
  /**
   * 应用缓动函数
   * @param {Number} t 进度 (0-1)
   * @param {String} easingName 缓动函数名称
   * @returns {Number} 缓动后的进度值
   */
  applyEasing(t, easingName) {
    switch (easingName) {
      case 'linear':
        return t
      case 'easeInQuad':
        return t * t
      case 'easeOutQuad':
        return t * (2 - t)
      case 'easeInOutQuad':
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
      case 'easeOutBack':
        const c1 = 1.70158
        const c3 = c1 + 1
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2)
      default:
        return t
    }
  }
  
  /**
   * 清除所有动画
   */
  clearAll() {
    this.animations = []
  }
  
  /**
   * 清除特定目标的动画
   * @param {Object} target 目标对象
   */
  clearTarget(target) {
    this.animations = this.animations.filter(anim => anim.target !== target)
  }
  
  // 检查是否有活跃动画
  hasActiveAnimations() {
    return this.animations.length > 0
  }
} 