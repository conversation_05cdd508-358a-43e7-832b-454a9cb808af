// 渲染工具函数

// 绘制圆角矩形
export function drawRoundRect(ctx, x, y, width, height, radius, color) {
  ctx.fillStyle = color
  ctx.beginPath()
  ctx.moveTo(x + radius, y)
  ctx.lineTo(x + width - radius, y)
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
  ctx.lineTo(x + width, y + height - radius)
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
  ctx.lineTo(x + radius, y + height)
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
  ctx.lineTo(x, y + radius)
  ctx.quadraticCurveTo(x, y, x + radius, y)
  ctx.closePath()
  ctx.fill()
}

// 绘制文本
export function drawText(ctx, text, x, y, font, color, align = 'center') {
  ctx.save()
  ctx.font = font
  ctx.fillStyle = color
  ctx.textAlign = align
  ctx.textBaseline = 'middle'  // 确保所有文本都使用相同的基线
  ctx.fillText(text, x, y)
  ctx.restore()
}

// 绘制按钮
export function drawButton(ctx, text, x, y, width, height, color, textColor, font) {
  // 绘制按钮背景
  drawRoundRect(ctx, x, y, width, height, 8, color)
  
  // 绘制按钮文本
  drawText(
    ctx,
    text,
    x + width / 2,  // 水平居中
    y + height / 2, // 垂直居中
    font,
    textColor,
    'center'
  )
} 